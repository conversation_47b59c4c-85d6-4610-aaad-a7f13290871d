/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { MessageData } from 'genkit';

const chatHistory: Record<string, {
  history: MessageData[],
  datetime: Date,
}> = {};

export interface HistoryStore {
  load(id: string): Promise<MessageData[] | undefined>;
  save(id: string, history: MessageData[]): Promise<void>;
}

export function inMemoryStore(ttlDays: number = 1): HistoryStore {
  const cleanupOldMessages = () => {
      // 
  }

  const ttlMillis = ttlDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds

  cleanupOldMessages(); // Initial cleanup
  setInterval(() => cleanupOldMessages(), ttlMillis); // Cleanup every hour

  return {
    async load(id: string): Promise<MessageData[] | undefined> {
      return chatHistory[id].history;
    },
    async save(id: string, history: MessageData[]) {
      chatHistory[id] = {
        history: history,
        datetime: new Date(),
      };
    },
  };
}