/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { MessageData } from 'genkit';

const chatHistory: Record<string, {
  history: MessageData[],
  datetime: Date,
}> = {};

export interface HistoryStats {
  totalSessions: number;
  oldestSession: Date | null;
  newestSession: Date | null;
}

export interface HistoryStore {
  load(id: string): Promise<MessageData[] | undefined>;
  save(id: string, history: MessageData[]): Promise<void>;
  getStats(): Promise<HistoryStats>;
}

export function inMemoryStore(ttlDays: number = 1) {
  const cleanupOldMessages = () => {
    const now = new Date();
    let cleanupCount = 0;

    // Iterate through all chat histories
    for (const id in chatHistory) {
      const entry = chatHistory[id];
      const age = now.getTime() - entry.datetime.getTime();

      // If the entry is older than TTL, remove it
      if (age > ttlMillis) {
        delete chatHistory[id];
        cleanupCount++;
      }
    }

    if (cleanupCount > 0) {
      console.log(`Cleaned up ${cleanupCount} old chat histories (TTL: ${ttlDays} days)`);
    }
  }

  const ttlMillis = ttlDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds

  cleanupOldMessages(); // Initial cleanup

  // Run cleanup every hour (3600000 ms)
  const HOUR_IN_MS = 60 * 60 * 1000;
  setInterval(() => cleanupOldMessages(), HOUR_IN_MS);

  return {
    async load(id: string): Promise<MessageData[] | undefined> {
      // Check if the chat history exists for this ID
      if (!chatHistory[id]) {
        return undefined;
      }
      return chatHistory[id].history;
    },
    async save(id: string, history: MessageData[]) {
      chatHistory[id] = {
        history: history,
        datetime: new Date(),
      };
    },
    async getStats(): Promise<HistoryStats> {
      const ids = Object.keys(chatHistory);
      let oldestDate: Date | null = null;
      let newestDate: Date | null = null;

      // Find oldest and newest sessions
      for (const id of ids) {
        const date = chatHistory[id].datetime;

        if (!oldestDate || date < oldestDate) {
          oldestDate = date;
        }

        if (!newestDate || date > newestDate) {
          newestDate = date;
        }
      }

      return {
        totalSessions: ids.length,
        oldestSession: oldestDate,
        newestSession: newestDate
      };
    }
  };
}