import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";

const client = new SecretsManagerClient({
  region: process.env.AWS_REGION, // or hardcode if needed
});

export async function loadSecretsToEnv(secretId?: string): Promise<void> {
  try {
    if (!secretId) return;

    const command = new GetSecretValueCommand({ SecretId: secretId });
    const response = await client.send(command);

    if (response.SecretString) {
      const secret = JSON.parse(response.SecretString);

      for (const [key, value] of Object.entries(secret)) {
        process.env[key] = value as string;
      }
    } else {
      console.warn("Secret binary received. Not supported in this loader.");
    }
  } catch (error) {
    console.error("Failed to load secrets from AWS Secrets Manager:", error);
    throw error;
  }
}
