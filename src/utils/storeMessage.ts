import { pool } from "..";

export async function storeMessage({
  chatId,
  userId,
  userMessage,
  aiMessage,
}: {
  chatId: string;
  userId: string;
  userMessage: string;
  aiMessage: string;
}) {
  try {
    const [rows] = await pool.query(
      'INSERT INTO chat_messages (chat_id, user_id, user_message, model_message, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
      [chatId, userId, userMessage, aiMessage]
    );
    return rows;
  } catch (error) {
    console.error('Error storing message:', error);
    throw error;
  }
}