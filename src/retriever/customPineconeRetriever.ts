import {
  Pinecone,
  PineconeConfiguration
} from '@pinecone-database/pinecone';
import { Document, EmbedderArgument, Genkit, z } from "genkit";
import { CommonRetrieverOptionsSchema } from "genkit/retriever";
import { getPineconeDefaultConfig } from "../helper";

const CONTENT_KEY = '_content';
const CONTENT_TYPE = '_contentType';

const PineconeRetrieverOptionsSchema = CommonRetrieverOptionsSchema.extend({
  k: z.number().max(1000),
  namespace: z.string().optional(),
  filter: z.record(z.string(), z.any()).optional(),
  // includeValues is always false
  // includeMetadata is always true
});

/**
 * Configures a Pinecone retriever.
 * @param ai A Genkit instance
 * @param params The params for the retriever
 * @param params.indexId The name of the retriever
 * @param params.clientParams PineconeConfiguration containing the
PINECONE_API_KEY. If not set, the PINECONE_API_KEY environment variable will
be used instead.
 * @param params.textKey Deprecated. Please use contentKey.
 * @param params.contentKey The metadata key that contains the
content. If not specified, the value '_content' is used by default.
 * @param params.embedder The embedder to use for the retriever
 * @param params.embedderOptions  Options to customize the embedder
 * @returns A Pinecone retriever
 */
export function configureCustomPineconeRetriever<
  EmbedderCustomOptions extends z.ZodTypeAny,
>(
  ai: Genkit,
  params: {
    clientParams?: PineconeConfiguration;
    indexId: string;
    contentKey?: string;
    embedder: EmbedderArgument<EmbedderCustomOptions>;
    embedderOptions?: z.infer<EmbedderCustomOptions>;
  }
) {
  const { indexId, embedder, embedderOptions } = params;

  const pineconeConfig = getPineconeDefaultConfig();
  const contentKey = CONTENT_KEY;
  const pinecone = new Pinecone(pineconeConfig);
  const index = pinecone.index(indexId);

  return ai.defineRetriever(
    {
      name: `pinecone/${params.indexId}/custom`,
      configSchema: PineconeRetrieverOptionsSchema,
    },
    async (content, options) => {
      const queryEmbeddings = await ai.embed({
        embedder,
        content,
        options: embedderOptions,
      });
      const scopedIndex = !!options.namespace
        ? index.namespace(options.namespace)
        : index;
      const response = await scopedIndex.query({
        topK: options.k,
        vector: queryEmbeddings[0].embedding,
        includeValues: false,
        includeMetadata: true,
      });
      return {
        documents: response.matches
          .filter((m) => !!m.metadata)
          .map((m) => {
            const metadata = m.metadata!;
            const docMeta = JSON.parse(metadata.docMetadata as string) as Record<
              string,
              unknown
            >
            return Document.fromData(
              metadata[contentKey] as string,
              metadata[CONTENT_TYPE] as string,
              {
                ...docMeta,
                score: m.score
              }
            );
          }),
      };
    }
  );
}