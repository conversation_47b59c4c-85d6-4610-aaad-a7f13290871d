import { startFlowServer } from '@genkit-ai/express';
import { googleAI, textEmbedding004 } from '@genkit-ai/googleai';
import { genkit } from 'genkit';
import mysql from "mysql2/promise";
import { chatWithMeridiqFlow } from './chatWithMeridiq';
import { authenticateUserExpress } from './helper';
import { indexMeridiqDocsFlow } from './indexMeridiqDocs';
import { chatPrompt } from './prompts/chatPrompt';
import { indexNamespace } from './consts';
import devLocalVectorstore from './devLocalVectorStore';

const pool = mysql.createPool({
  host: process.env.DB_HOST ?? '',
  user: process.env.DB_USERNAME ?? '',
  password: process.env.DB_PASSWORD ?? '',
  database: process.env.DB_DATABASE ?? '',
  waitForConnections: true,
  connectionLimit: 10, // Adjust as needed
  queueLimit: 0
})

const ai = genkit({
  plugins: [
    googleAI(), // Provide the key via the GOOGLE_GENAI_API_KEY environment variable or arg { apiKey: 'yourkey'}
    devLocalVectorstore([
      {
        indexName: indexNamespace,
        embedder: 'googleai/gemini-embedding-001',
      },
    ]),
    // pinecone([
    //   {
    //     indexId: 'data',
    //     embedder: textEmbedding004,
    //   },
    // ]),
  ],
});

// configureCustomPineconeRetriever(ai, {
//   indexId: 'data',
//   embedder: textEmbedding004,
// });

const meridiqPrompt = chatPrompt(ai);


// Start a flow server, which exposes your flows as HTTP endpoints. This call
// must come last, after all of your plug-in configuration and flow definitions.
// You can optionally specify a subset of flows to serve, and configure some
// HTTP server options, but by default, the flow server serves all defined flows.
startFlowServer({
  flows: [indexMeridiqDocsFlow(ai), authenticateUserExpress(chatWithMeridiqFlow(ai))],
  cors: {
    "origin": "*",
    "methods": "GET,HEAD,PUT,PATCH,POST,DELETE",
    "preflightContinue": false,
    "optionsSuccessStatus": 204
  },
  port: parseInt(process.env.PORT ?? "3400"),
});

export { ai, meridiqPrompt, pool };
