import { startFlowServer } from '@genkit-ai/express';
import { googleAI, textEmbedding004 } from '@genkit-ai/googleai';
import { genkit } from 'genkit';
import { pinecone } from 'genkitx-pinecone';
import { chatWithMeridiqFlow } from './chatWithMeridiq';
import { authenticateUserExpress } from './helper';
import { indexMeridiqDocsFlow } from './indexMeridiqDocs';
import { chatPrompt } from './prompts/chatPrompt';
import { configureCustomPineconeRetriever } from './retriever/customPineconeRetriever';
import { loadSecretsToEnv } from './utils/loadSecrets';
import mysql from "mysql2/promise";

const bootstrap = () => {
  loadSecretsToEnv(process.env.AWS_SECRETS_MANAGER_SECRET_ID);
  
  const pool = mysql.createPool({
    host: process.env.DB_HOST ?? '',
    user: process.env.DB_USERNAME ?? '',
    password: process.env.DB_PASSWORD ?? '',
    database: process.env.DB_DATABASE ?? '',
    waitForConnections: true,
    connectionLimit: 10, // Adjust as needed
    queueLimit: 0
  })

  const ai = genkit({
    plugins: [
      googleAI(), // Provide the key via the GOOGLE_GENAI_API_KEY environment variable or arg { apiKey: 'yourkey'}
      pinecone([
        {
          indexId: 'data',
          embedder: textEmbedding004,
        },
      ]),
    ],
  });
  
  configureCustomPineconeRetriever(ai, {
    indexId: 'data',
    embedder: textEmbedding004,
  });
  
  const meridiqPrompt = chatPrompt(ai);
  
  
  // Start a flow server, which exposes your flows as HTTP endpoints. This call
  // must come last, after all of your plug-in configuration and flow definitions.
  // You can optionally specify a subset of flows to serve, and configure some
  // HTTP server options, but by default, the flow server serves all defined flows.
  startFlowServer({
    flows: [indexMeridiqDocsFlow(ai), authenticateUserExpress(chatWithMeridiqFlow(ai))],
  });

  return { ai, meridiqPrompt, pool };
}

export const { ai, meridiqPrompt, pool } = bootstrap();
