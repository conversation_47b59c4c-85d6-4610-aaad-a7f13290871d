/**
 * Test script to verify that the indexing functionality is working correctly
 * This script tests the complete indexing pipeline including:
 * - Document creation and indexing
 * - Retrieval functionality
 * - Vector similarity search
 * - File storage operations
 */

import { googleAI, textEmbedding004 } from '@genkit-ai/googleai';
import { genkit, Document } from 'genkit';
import { indexNamespace } from './consts';
import devLocalVectorstore, { 
  devLocalIndexerRef, 
  devLocalRetrieverRef, 
  loadFilestore,
  deleteFilestore 
} from './devLocalVectorStore';

// Test configuration
const TEST_INDEX_NAME = 'test-meridiq-index';
const TEST_DOCUMENTS = [
  {
    title: 'MERIDIQ User Guide',
    content: 'MERIDIQ is a comprehensive practice management software for aesthetic clinics. It helps manage patient records, appointments, and treatment plans.',
    section: 'Introduction'
  },
  {
    title: 'Patient Management',
    content: 'The patient management module allows you to create detailed patient profiles, track medical history, and manage consent forms.',
    section: 'Features'
  },
  {
    title: 'Appointment Scheduling',
    content: 'Schedule appointments with ease using the integrated calendar system. Set recurring appointments and manage staff schedules.',
    section: 'Scheduling'
  },
  {
    title: 'Treatment Documentation',
    content: 'Document treatments with before and after photos, treatment notes, and follow-up instructions for comprehensive patient care.',
    section: 'Documentation'
  },
  {
    title: 'Billing and Invoicing',
    content: 'Generate invoices, track payments, and manage insurance claims through the integrated billing system.',
    section: 'Financial'
  }
];

// Initialize Genkit AI
const ai = genkit({
  plugins: [
    googleAI(),
    devLocalVectorstore([
      {
        indexName: TEST_INDEX_NAME,
        embedder: textEmbedding004,
      },
    ]),
  ],
});

/**
 * Test helper functions
 */
class IndexingTester {
  private indexer = devLocalIndexerRef(TEST_INDEX_NAME);
  private retriever = devLocalRetrieverRef(TEST_INDEX_NAME);

  /**
   * Clean up any existing test data
   */
  async cleanup(): Promise<void> {
    try {
      deleteFilestore(TEST_INDEX_NAME);
      console.log('✅ Cleaned up existing test data');
    } catch (error) {
      console.log('ℹ️  No existing test data to clean up');
    }
  }

  /**
   * Create test documents from sample data
   */
  createTestDocuments(): Document[] {
    return TEST_DOCUMENTS.map((doc, index) => {
      return Document.fromText(
        `Title: ${doc.title}\nContent: ${doc.content}`,
        {
          url: `https://test.meridiq.com/docs/${index}`,
          title: doc.title,
          section: doc.section,
          key: TEST_INDEX_NAME,
          testId: `test-doc-${index}`
        }
      );
    });
  }

  /**
   * Test document indexing
   */
  async testIndexing(): Promise<boolean> {
    try {
      console.log('\n🔄 Testing document indexing...');
      
      const documents = this.createTestDocuments();
      console.log(`📄 Created ${documents.length} test documents`);

      // Index the documents
      await ai.index({
        indexer: this.indexer,
        documents,
      });

      console.log('✅ Documents indexed successfully');
      
      // Verify the index file was created
      const indexData = loadFilestore(TEST_INDEX_NAME);
      const indexedCount = Object.keys(indexData).length;
      
      if (indexedCount === 0) {
        throw new Error('No documents found in index after indexing');
      }
      
      console.log(`✅ Index contains ${indexedCount} documents`);
      return true;
    } catch (error) {
      console.error('❌ Indexing test failed:', error);
      return false;
    }
  }

  /**
   * Test document retrieval with various queries
   */
  async testRetrieval(): Promise<boolean> {
    try {
      console.log('\n🔄 Testing document retrieval...');
      
      const testQueries = [
        {
          query: 'patient management software',
          expectedSections: ['Introduction', 'Features'],
          description: 'General patient management query'
        },
        {
          query: 'schedule appointments calendar',
          expectedSections: ['Scheduling'],
          description: 'Appointment scheduling query'
        },
        {
          query: 'billing invoices payments',
          expectedSections: ['Financial'],
          description: 'Financial management query'
        },
        {
          query: 'treatment photos documentation',
          expectedSections: ['Documentation'],
          description: 'Treatment documentation query'
        }
      ];

      let allTestsPassed = true;

      for (const testQuery of testQueries) {
        console.log(`\n🔍 Testing: ${testQuery.description}`);
        console.log(`   Query: "${testQuery.query}"`);
        
        const results = await ai.retrieve({
          retriever: this.retriever,
          query: testQuery.query,
          options: { k: 3 }
        });

        if (results.length === 0) {
          console.error(`❌ No results returned for query: "${testQuery.query}"`);
          allTestsPassed = false;
          continue;
        }

        console.log(`   📊 Retrieved ${results.length} documents`);
        
        // Check if results contain expected sections
        const retrievedSections = results.map(doc => doc.metadata?.section).filter(Boolean);
        const hasExpectedSection = testQuery.expectedSections.some(section => 
          retrievedSections.includes(section)
        );

        if (hasExpectedSection) {
          console.log(`   ✅ Found expected sections: ${retrievedSections.join(', ')}`);
        } else {
          console.log(`   ⚠️  Expected sections not found. Got: ${retrievedSections.join(', ')}`);
        }

        // Display top result details
        const topResult = results[0];
        const score = topResult.metadata?.score || 0;
        console.log(`   🎯 Top result: "${topResult.metadata?.title}" (score: ${score.toFixed(3)})`);
        
        if (score < 0.1) {
          console.log(`   ⚠️  Low similarity score: ${score.toFixed(3)}`);
        }
      }

      return allTestsPassed;
    } catch (error) {
      console.error('❌ Retrieval test failed:', error);
      return false;
    }
  }

  /**
   * Test edge cases and error handling
   */
  async testEdgeCases(): Promise<boolean> {
    try {
      console.log('\n🔄 Testing edge cases...');
      
      // Test empty query
      try {
        const emptyResults = await ai.retrieve({
          retriever: this.retriever,
          query: '',
          options: { k: 1 }
        });
        console.log(`✅ Empty query handled gracefully (${emptyResults.length} results)`);
      } catch (error) {
        console.log('⚠️  Empty query caused error:', error);
      }

      // Test very long query
      const longQuery = 'patient management software '.repeat(50);
      try {
        const longResults = await ai.retrieve({
          retriever: this.retriever,
          query: longQuery,
          options: { k: 1 }
        });
        console.log(`✅ Long query handled gracefully (${longResults.length} results)`);
      } catch (error) {
        console.log('⚠️  Long query caused error:', error);
      }

      // Test query with no matches
      const noMatchQuery = 'quantum physics nuclear fusion space exploration';
      const noMatchResults = await ai.retrieve({
        retriever: this.retriever,
        query: noMatchQuery,
        options: { k: 3 }
      });
      console.log(`✅ No-match query returned ${noMatchResults.length} results`);

      return true;
    } catch (error) {
      console.error('❌ Edge cases test failed:', error);
      return false;
    }
  }

  /**
   * Test index statistics and health
   */
  async testIndexHealth(): Promise<boolean> {
    try {
      console.log('\n🔄 Testing index health...');
      
      const indexData = loadFilestore(TEST_INDEX_NAME);
      const documentCount = Object.keys(indexData).length;
      
      console.log(`📊 Index statistics:`);
      console.log(`   - Total documents: ${documentCount}`);
      
      // Check if all documents have embeddings
      let documentsWithEmbeddings = 0;
      let totalEmbeddingDimensions = 0;
      
      for (const [id, data] of Object.entries(indexData)) {
        if (data.embedding && data.embedding.embedding) {
          documentsWithEmbeddings++;
          totalEmbeddingDimensions = data.embedding.embedding.length;
        }
      }
      
      console.log(`   - Documents with embeddings: ${documentsWithEmbeddings}`);
      console.log(`   - Embedding dimensions: ${totalEmbeddingDimensions}`);
      
      if (documentsWithEmbeddings !== documentCount) {
        console.error(`❌ Not all documents have embeddings`);
        return false;
      }
      
      if (totalEmbeddingDimensions === 0) {
        console.error(`❌ Invalid embedding dimensions`);
        return false;
      }
      
      console.log('✅ Index health check passed');
      return true;
    } catch (error) {
      console.error('❌ Index health test failed:', error);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting MERIDIQ Indexing Tests\n');
    console.log('=' .repeat(50));
    
    const results = {
      cleanup: true,
      indexing: false,
      retrieval: false,
      edgeCases: false,
      indexHealth: false
    };

    try {
      // Cleanup
      await this.cleanup();
      
      // Run tests in sequence
      results.indexing = await this.testIndexing();
      if (results.indexing) {
        results.retrieval = await this.testRetrieval();
        results.edgeCases = await this.testEdgeCases();
        results.indexHealth = await this.testIndexHealth();
      }
      
    } catch (error) {
      console.error('❌ Test suite failed with error:', error);
    } finally {
      // Final cleanup
      await this.cleanup();
    }

    // Print summary
    console.log('\n' + '=' .repeat(50));
    console.log('📋 Test Results Summary:');
    console.log('=' .repeat(50));
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      console.log(`${test.padEnd(15)}: ${status}`);
    });
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log('\n' + '=' .repeat(50));
    console.log(`🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Indexing system is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the indexing configuration.');
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const tester = new IndexingTester();
  await tester.runAllTests();
}

// Export for use in other files
export { IndexingTester, TEST_DOCUMENTS, TEST_INDEX_NAME };

/**
 * Quick test function for basic functionality
 */
export async function quickTest(): Promise<boolean> {
  console.log('🚀 Running Quick Index Test...\n');

  const tester = new IndexingTester();
  await tester.cleanup();

  const indexingSuccess = await tester.testIndexing();
  if (!indexingSuccess) {
    await tester.cleanup();
    return false;
  }

  // Test one simple retrieval
  try {
    const results = await ai.retrieve({
      retriever: devLocalRetrieverRef(TEST_INDEX_NAME),
      query: 'patient management',
      options: { k: 1 }
    });

    const success = results.length > 0;
    console.log(success ? '✅ Quick test PASSED' : '❌ Quick test FAILED');

    await tester.cleanup();
    return success;
  } catch (error) {
    console.error('❌ Quick test failed:', error);
    await tester.cleanup();
    return false;
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
