import { Genkit, z } from 'genkit';
import { pineconeRetrieverRef } from 'genkitx-pinecone';
import { detect } from 'tinyld/light';
import { getStartingMessages, sendToSlack } from './helper';
import { indexNamespace } from './indexMeridiqDocs';
import { inMemoryStore } from './memory';
import { meridiqDocPromptString, meridiqPromptString } from './prompts/chatPrompt';
import { storeMessage } from './utils/storeMessage';

const MAX_HISTORY_LENGTH = 6;


// Define the retriever reference
// const websiteRetriever = devLocalRetrieverRef(`data`);
const websiteRetriever = pineconeRetrieverRef({
  indexId: `data/custom`,
  displayName: "Website Retriever",
});

const historyStore = inMemoryStore();

export const chatWithMeridiqFlow = (ai: Genkit) => ai.defineFlow({
  name: 'chatWithMeridiqFlow',
  inputSchema: z.object({
    input: z.string().nonempty(),
    lang: z.string().optional(),
    sessionId: z.string(),
  }),
  // outputSchema: z.string(),
},
  async (data, { sendChunk, context }) => {
    const input = data.input;
    let chatHistory = await historyStore.load(data.sessionId) || [];

    // retrieve relevant documents
    const { docs, translatedQuestion, lang, type } = await ai.run('retrieve-ducuments', async () => {
      let question = input;
      const detectedLanguage = detect(input) || 'en';

      const { output } = await ai.generate({
        model: 'googleai/gemini-2.0-flash',
        prompt: `${question}`,
        system: meridiqDocPromptString,
        messages: chatHistory.filter(m => ['model', 'user'].includes(m.role)),
        output: {
          schema: z.object({
            question: z.string(),
            typeOfQuestion: z.enum(['question', 'greeting', 'farewell', 'contact']).describe('The type of the question eg: "question", "greeting", "farewell", "contact"'),
          }),
        },
      })

      if (['greeting', 'farewell', 'contact'].includes(output?.typeOfQuestion ?? '')) {
        return {
          translatedQuestion: input,
          docs: [],
          lang: detectedLanguage,
          type: output?.typeOfQuestion,
        };
      }

      question = output?.question ?? input;

      const docs = await ai.retrieve({
        retriever: websiteRetriever,
        query: question,
        // options: { k: 10 },
        options: { k: 10, namespace: indexNamespace, },
      });

      return {
        translatedQuestion: question,
        docs: docs.filter((v) => v.metadata?.score >= 0.5),
        lang: detectedLanguage,
        type: output?.typeOfQuestion,
      }
    });

    if (!chatHistory.find((c) => c.role === 'system')) {
      const startingMessages = getStartingMessages(data.lang ?? lang);

      chatHistory = [{
        role: 'system',
        content: [{
          text: meridiqPromptString,
        }, ...startingMessages],
      }];
    }

    let prompt = input;

    if (input != translatedQuestion) {
      prompt = `orignalQuestion: ${input} \n translatedQuestion: ${translatedQuestion}`;
    }

    const { response, stream } = ai.generateStream({
      prompt: prompt,
      // prompt: `${input}`,
      messages: chatHistory,
      model: 'googleai/gemini-2.0-flash',
      docs: docs.length ? [{
        content: [{
          text: docs.flatMap((doc) => doc.content.map((c) => c.text)).join(' \n -: '),
          metadata: { source: 'context', purpose: 'context' },
        }]
      }] : undefined,
      config: {
        temperature: 0,
      },
      output: {
        schema: z.object({
          revelencyScore: z.number().describe('How relevant is the answer to the question? 0 to 100'),
          answer: z.string().describe("The answer to the question"),
        }),
      },
    })

    // Track what has been sent to avoid duplicates
    let previousAnswerLength = 0;

    for await (const chunk of stream) {
      // Check if we have an output object with an answer field
      if (chunk.output && typeof chunk.output === 'object' && 'answer' in chunk.output) {
        const currentAnswer = chunk.output.answer as string;

        // Only send the new part of the answer that wasn't in the previous chunk
        if (currentAnswer.length > previousAnswerLength) {
          // Send only the incremental part
          sendChunk(currentAnswer.substring(previousAnswerLength));

          // Update the previous answer length for the next chunk
          previousAnswerLength = currentAnswer.length;
        }
      }
    }

    const awaitedResponse = await response;

    if (awaitedResponse.output && 'revelencyScore' in awaitedResponse.output && awaitedResponse.output?.revelencyScore < 50) {
      await sendToSlack(input);
    }

    const messages = awaitedResponse.messages;

    // Process messages to clean up context metadata
    const processedMessages = messages.map((message) => {
      if (message.role === 'user') {
        message.content = message.content.map((content) => {
          if (content.metadata?.source === 'context' || content.metadata?.purpose === 'context') {
            return null;
          }
          return content;
        }).filter((c) => !!c);
      }
      return message;
    }).filter(m => !!m);

    // Get existing history to extract the system message
    const systemMessage = messages.find(msg => msg.role === 'system');

    // Create new history with system message and last 4 messages
    let newHistory = [];

    // Add system message if it exists
    if (systemMessage) {
      newHistory.push(systemMessage);
    }

    // Add the last 4 messages from the processed messages
    const lastMessages = processedMessages.filter((m) => m.role !== 'system').slice(-MAX_HISTORY_LENGTH);
    newHistory = [...newHistory, ...lastMessages];

    // Save the new history
    await historyStore.save(data.sessionId, newHistory);

    await storeMessage({
      chatId: data.sessionId,
      userId: context?.auth?.id ?? '',
      userMessage: input,
      aiMessage: awaitedResponse.output?.answer ?? '',
    });

    return awaitedResponse.output?.answer ?? '';
  }
);