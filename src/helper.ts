import { withContextProvider } from '@genkit-ai/express';
import {
  Pinecone,
  PineconeConfiguration
} from '@pinecone-database/pinecone';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { Flow, Part, UserFacingError } from 'genkit';

import { convert as htmlToText } from 'html-to-text';
import jwt from 'jsonwebtoken';
import { extract } from "@extractus/article-extractor";

/**
 * Crawls the given domain, extracts text content from all pages, and returns an array of documents.
 */
export async function crawlAndIndexWebsite(startUrl: string): Promise<ArticleData[]> {
  // Set to store visited links
  const visitedUrls = new Set<string>();

  const domain = new URL(startUrl).origin;
  const queue: string[] = [startUrl];
  const indexedData: ArticleData[] = [];

  while (queue.length > 0) {
    const currentUrl = normalizeUrl(queue.shift()!);

    if (visitedUrls.has(currentUrl)) {
      continue;
    }

    console.log(`Crawling: ${currentUrl}`);
    visitedUrls.add(currentUrl);

    // Fetch and process page content
    const pageContent = await extractPageContent(currentUrl);

    if (pageContent) {
      indexedData.push(pageContent);
    }

    // Extract new links to follow
    try {
      const response = await axios.get(currentUrl);
      const $ = cheerio.load(response.data);

      $('a[href]').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          const absoluteUrl = new URL(href, currentUrl).href;
          if (absoluteUrl.startsWith(domain) && !visitedUrls.has(absoluteUrl)) {
            queue.push(absoluteUrl);
          }
        }
      });
    } catch (error) {
      console.error(`Error processing links on ${currentUrl}:`, error);
    }
  }

  return indexedData;
}


/**
 * Fetch and clean the body content of a given URL.
 * @param url The URL to fetch and extract content from.
 */
async function extractPageContent(url: string): Promise<ArticleData | null> {
  try {
    // Extract the article content
    const article = await extract(url);

    if (!article || !article.content) {
      console.log(`Failed to extract content from ${url}`);
      throw new Error(`Failed to extract content from ${url}`);
    }

    // First, extract the headers and their content from the HTML
    const $ = cheerio.load(article.content);
    const sections: { header: string; content: string; level: number }[] = [];

    // Extract the article title as the first header
    if (article.title) {
      sections.push({
        header: article.title,
        content: '',
        level: 0
      });
    }

    // Find all headers in the document
    $('h1, h2, h3, h4, h5, h6').each((_, element) => {
      const headerText = $(element).text().trim();
      const tagName = element.tagName || '';
      const headerLevel = parseInt(tagName.substring(1));
      let contentHtml = '';

      // Get all content until the next header of same or higher level
      let nextElement = $(element).next();
      while (
        nextElement.length &&
        !(nextElement.is('h1, h2, h3, h4, h5, h6') &&
          (() => {
            const nextTagName = nextElement.prop('tagName');
            return typeof nextTagName === 'string' &&
              parseInt(nextTagName.substring(1)) <= headerLevel;
          })())
      ) {
        contentHtml += nextElement.prop('outerHTML') || '';
        nextElement = nextElement.next();
      }

      // Convert the content HTML to text
      const contentText = htmlToText(contentHtml, {
        wordwrap: null,
        selectors: [{ selector: 'a', options: { ignoreHref: true } }],
        preserveNewlines: true,
        decodeEntities: true,
      }).trim();

      sections.push({
        header: headerText,
        content: contentText,
        level: headerLevel
      });
    });

    // If no headers were found, add the entire content as a single section
    if (sections.length === (article.title ? 1 : 0)) {
      const fullContent = htmlToText(article.content, {
        wordwrap: null,
        selectors: [{ selector: 'a', options: { ignoreHref: true } }],
        preserveNewlines: true,
        decodeEntities: true,
      }).trim();

      sections.push({
        header: 'Content',
        content: fullContent,
        level: 1
      });
    }

    // Also create a full text version for backward compatibility
    const fullTextContent = sections.map(section => {
      const prefix = section.level > 0 ? '#'.repeat(section.level) + ' ' : '';
      return `${prefix}${section.header}\n\n${section.content}`;
    }).join('\n\n');

    return {
      ...article,
      content: fullTextContent,
      sections: sections
    };
  } catch (error) {
    console.error(`Failed to fetch ${url}:`, error);
    return null;
  }
}

/**
 * Helper function for deleting pinecone indices.
 * @param params The params for deleting a Pinecone index.
 * @param params.clientParams The params to initialize Pinecone.
 * @param params.name The name of the Pinecone index to delete.
 * @returns a void Promise that is fulfilled when the index has been deleted.
 */
export async function deletePineconeNamespace(params: {
  clientParams?: PineconeConfiguration;
  name: string;
  namespace: string;
}) {
  try {
    const pineconeConfig = params.clientParams ?? getPineconeDefaultConfig();
    const pinecone = new Pinecone(pineconeConfig);
    const data = await pinecone.index(params.name).namespace(params.namespace).deleteAll();

    await delay(30000);

    return data;
  } catch (error) {
    console.log(error);
  }
}

export function getPineconeDefaultConfig() {
  const maybeApiKey = process.env.PINECONE_API_KEY;
  if (!maybeApiKey)
    throw new Error(
      'Please pass in the API key or set PINECONE_API_KEY environment variable.\n' +
      'For more details see https://firebase.google.com/docs/genkit/plugins/pinecone'
    );
  return { apiKey: maybeApiKey } as PineconeConfiguration;
}

function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Normalize URL to remove hash fragments.
 */
function normalizeUrl(url: string): string {
  try {
    const parsedUrl = new URL(url);
    parsedUrl.hash = ''; // Remove the hash fragment
    return parsedUrl.href;
  } catch (error) {
    console.error(`Invalid URL: ${url}`);
    return '';
  }
}

export const getStartingMessages = (lang?: string) => {
  let startingMessages: Part[] = [];

  if (lang === 'sv') {
    startingMessages.push({
      text: "Hej! Jag är MERIDIQ Support Bot, alltid glad att hjälpa dig med din support och teknik.",
    });

    startingMessages.push({
      text: "Vad kan jag göra för dig idag?",
    });
  } else {
    startingMessages.push({
      text: "Hi! I'm the MERIDIQ Support Bot, always happy to help you with your support and tech.",
    });

    startingMessages.push({
      text: "What can I do for you today?",
    });
  }

  return startingMessages;
}



/**
 * Sends a message to a Slack channel when the AI is unable to answer a question
 * @param question The question that couldn't be answered
 */
export const sendToSlackUnanswered = async (question: string, email?: string): Promise<void> => {
  try {
    const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL

    if (!slackWebhookUrl) {
      console.log("SLACK_WEBHOOK_URL environment variable is not set")
      return
    }

    const message = {
      text: `:question: *Unanswered Question:*\n>${question}`,
      blocks: [
        ...(email ? [{
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:bust_in_silhouette: *User:*\n>${email}`
          }
        }] : []),
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:question: *Unanswered Question:*\n>${question}`
          }
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `_Sent from MERIDIQ Chatbot_`
            }
          ]
        }
      ]
    }

    await axios.post(slackWebhookUrl, message)
    console.log("Unanswered question sent to Slack successfully")
  } catch (error) {
    console.log("Error sending message to Slack:", error)
  }
}


/**
 * Sends a message to a Slack channel when the AI is unable to answer a question
 * @param question The question that couldn't be answered
 */
export const sendToSlackAll = async (question: string, answer: string, email?: string): Promise<void> => {
  try {
    const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL

    if (!slackWebhookUrl) {
      console.log("SLACK_WEBHOOK_URL environment variable is not set")
      return
    }

    const message = {
      text: `:grey_question: *Question:*\n>${question}\n\n:bulb: *Answer:*\n>${answer}`,
      blocks: [
        ...(email ? [{
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:bust_in_silhouette: *User:*\n>${email}`
          }
        }] : []),
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:grey_question: *Question:*\n>${question}`
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `:bulb: *Answer:*\n>${answer}`
          }
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `_Sent from MERIDIQ Chatbot_`
            }
          ]
        }
      ]
    }

    await axios.post(slackWebhookUrl, message)
    console.log("Unanswered question sent to Slack successfully")
  } catch (error) {
    console.log("Error sending message to Slack:", error)
  }
}

export interface Section {
  header: string;
  content: string;
  level: number;
}

export interface ArticleData {
  url?: string;
  links?: string[];
  title?: string;
  description?: string;
  image?: string;
  favicon?: string;
  author?: string;
  content?: string;
  source?: string;
  published?: string;
  ttr?: number;
  type?: string;
  sections?: Section[];
}

export const authenticateUserExpress = (flow: Flow) => {
  return withContextProvider(flow, (data) => {
    // authenticate jwt token in data.headers['authorization'] Bearer
    if (!data?.headers || !data?.headers['authorization']) {
      throw new UserFacingError('UNAUTHENTICATED', 'Unauthorized');
    }

    const token = data.headers['authorization']?.split(' ')[1];

    if (!token) {
      throw new UserFacingError('UNAUTHENTICATED', 'Unauthorized');
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET ?? '') as jwt.JwtPayload;

      const userId = decoded.sub;

      return {
        auth: {
          userId: userId,
          email: decoded?.email,
        },
      };
    } catch (error) {
      throw new UserFacingError('UNAUTHENTICATED', 'Unauthorized');
    }
  });
}