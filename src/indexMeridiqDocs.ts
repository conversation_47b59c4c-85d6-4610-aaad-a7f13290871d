import { Document, Genkit } from 'genkit';
import { crawlAndIndexWebsite } from './helper';
import { indexNamespace } from './consts';
import { deleteFilestore, devLocalIndexerRef } from './devLocalVectorStore';

const websiteIndexer = devLocalIndexerRef(indexNamespace);

// const websiteIndexer = pineconeIndexerRef({
//   indexId: 'data',
// });

const chunkingConfig = {
  minLength: 500,     // Smaller minLength to capture concise sections or paragraphs
  maxLength: 1500,    // Ensure chunks fit within token limits (approx. 375 tokens)
  splitter: 'paragraph', // Split by paragraphs for topical coherence
  overlap: 50,       // Increased overlap to preserve context across chunks
  // delimiters: '\n', // Use double newline as the paragraph delimiter
} as any;

export const indexMeridiqDocsFlow = (ai: Genkit) => ai.defineFlow(
  {
    name: 'indexMeridiqDocsFlow',
  },
  async (data) => {
    const url = process.env.MERIDIQ_DOCS_SITE ?? '';

    // Step 1: Crawl the website and fetch text content from all pages
    const pages = await ai.run('crawl-website', () => crawlAndIndexWebsite(url));

    // Step 2: Divide the content of each page into chunks
    const documents = [];
    for (const page of pages) {
      console.log(`Chunking content from: ${page.url}`);

      const chunks = await ai.run('chunk-it', async () =>
        page.sections?.filter((s) => !!s.content && !!s.header) ?? []
        // chunk(page.content ?? '', chunkingConfig) // Assuming `chunk` and `chunkingConfig` are defined elsewhere
      );

      const { content, sections, ...otherData } = page;

      // Convert chunks into documents to store in the index
      const pageDocuments = chunks.map((section, index) => {
        let prefix = "";
        if (index != 0) {
          const prevSection = chunks[index - 1];
          // get last 20 words  from prevSection
          prefix = prevSection.content.split(' ').slice(-20).join(' ');
        }

        return Document.fromText(
          `${!!prefix ? `Previous Context: ${prefix} \n ` : ''}Title: ${section.header} \n Content: ${section.content}`, 
          { 
            ...otherData, 
            key: indexNamespace, 
            section: section.header 
          }
        );
      });
      documents.push(...pageDocuments);
    }

    console.log(`Deleting collection: ${indexNamespace}`);
    // Delete the collection if it exists
    try {
      deleteFilestore(indexNamespace);
      // await deleteChromaCollection({
      //   name: indexNamespace,
      // });
    } catch (error) {
      
    }

    console.log(`Indexing documents`);
    // Step 3: Add documents to the AI index
    await ai.index({
      indexer: websiteIndexer, // Assuming `websiteIndexer` is defined
      documents,
    });

    return {
      message: `Indexed ${documents.length} documents from ${pages.length} pages`,
      status: 'success',
    }
  }
);