import { Document, Genkit } from 'genkit';
import { pineconeIndexerRef } from 'genkitx-pinecone';
import { crawlAndIndexWebsite, deletePineconeNamespace } from './helper';

// const websiteIndexer = devLocalIndexerRef('data');
const websiteIndexer = pineconeIndexerRef({
  indexId: 'data',
});

export const indexNamespace = 'meridiq'; // Replace with your desired namespace

const chunkingConfig = {
  minLength: 500,     // Smaller minLength to capture concise sections or paragraphs
  maxLength: 1500,    // Ensure chunks fit within token limits (approx. 375 tokens)
  splitter: 'paragraph', // Split by paragraphs for topical coherence
  overlap: 50,       // Increased overlap to preserve context across chunks
  // delimiters: '\n', // Use double newline as the paragraph delimiter
} as any;

export const indexMeridiqDocsFlow = (ai: Genkit) => ai.defineFlow(
  {
    name: 'indexMeridiqDocsFlow',
  },
  async (data) => {

    const url = process.env.MERIDIQ_DOCS_SITE ?? '';

    // Step 1: Crawl the website and fetch text content from all pages
    const pages = await ai.run('crawl-website', () => crawlAndIndexWebsite(url));
    // const { output } = await ai.generate({
    //   prompt: `Generate 1000 questions based on the Context.
    //   Context: 
    //   ${pages.map((page) => `${page.title} - ${page.content} - URL: ${page.url}`).join('\n\n')}`,
    //   model: 'googleai/gemini-1.5-flash',
    //   system: 'You are a helpful assistant that generates questions based on the content.',      
    //   output: {
    //     schema: z.object({
    //       questions: z.array(z.string()),
    //     }),
    //   },
    // });

    // return output;

    // Step 2: Divide the content of each page into chunks
    const documents = [];
    for (const page of pages) {
      console.log(`Indexing content from: ${page.url}`);

      const chunks = await ai.run('chunk-it', async () =>
        page.sections?.filter((s) => !!s.content && !!s.header) ?? []
        // chunk(page.content ?? '', chunkingConfig) // Assuming `chunk` and `chunkingConfig` are defined elsewhere
      );

      const { content, sections, ...otherData } = page;

      // Convert chunks into documents to store in the index
      const pageDocuments = chunks.map((section, index) => {
        let prefix = "";
        if (index != 0) {
          const prevSection = chunks[index - 1];
          // get last 20 words  from prevSection
          prefix = prevSection.content.split(' ').slice(-20).join(' ');
        }

        return Document.fromText(`${!!prefix ? `Previous Context: ${prefix} \n ` : ''}Title: ${section.header} \n Content: ${section.content}`, { ...otherData, key: indexNamespace, section: section.header });
      });
      documents.push(...pageDocuments);
    }

    deletePineconeNamespace({
      name: 'data',
      namespace: indexNamespace,
    });

    // Step 3: Add documents to the AI index
    await ai.index({
      indexer: websiteIndexer, // Assuming `websiteIndexer` is defined
      documents,
      options: {
        namespace: indexNamespace,
      }
    });

    return {
      message: `Indexed ${documents.length} documents from ${pages.length} pages`,
      status: 'success',
    }
  }
);