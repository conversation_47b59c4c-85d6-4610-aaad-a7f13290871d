# Stage 1: Build the project
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for building)
RUN npm install

# Copy the rest of your application source code
COPY . .

# Run the build script to compile TypeScript files into the lib folder
RUN npm run build

# Stage 2: Build the runtime image
FROM node:22-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm install --production

# Copy the compiled output from the builder stage
COPY --from=builder /app/lib ./lib

# Expose the port your app listens on (adjust if needed)
EXPOSE 3400

# Set the container entry point to the compiled file (adjust as necessary)
CMD ["node", "lib/index.js"]
