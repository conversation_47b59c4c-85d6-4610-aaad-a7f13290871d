{"name": "meridiq-chatbot-backend", "version": "1.0.0", "description": "Backend for MERIDIQ chatbot using Genkit", "main": "lib/index.js", "type": "module", "scripts": {"start": "node --env-file=.env --import ./lib/preload.js lib/index.js", "dev": "tsx --env-file=.env --no-warnings --watch src/index.ts", "genkit:dev": "genkit start -- npm run dev", "compile": "tsup-node src --out-dir lib --format esm", "build": "npm run compile", "build:watch": "tsc --env-file=.env --watch", "docker-build": "docker build -t meridiq-chatbot-backend .", "docker-run": "docker run --env-file .env -d --restart unless-stopped -p 3400:3400 --name meridiq-chatbot-backend meridiq-chatbot-backend:latest", "docker-stop": "docker stop meridiq-chatbot-backend && docker rm meridiq-chatbot-backend", "docker-build-run": "npm run docker-build && npm run docker-stop && npm run docker-run", "chromadb": "chroma run --path ./.db"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.879.0", "@extractus/article-extractor": "^8.0.19", "@genkit-ai/express": "^1.18.0", "@genkit-ai/googleai": "^1.18.0", "@netlify/functions": "^4.2.5", "axios": "^1.11.0", "cheerio": "^1.1.2", "compute-cosine-similarity": "^1.1.0", "genkit": "^1.18.0", "genkit-cli": "^1.18.0", "genkitx-pinecone": "^1.18.0", "html-to-text": "^9.0.5", "jsonwebtoken": "^9.0.2", "llm-chunk": "^0.0.1", "mysql2": "^3.14.4", "serverless-http": "^4.0.0", "tinyld": "^1.3.4"}, "devDependencies": {"@types/express": "^5.0.3", "@types/html-to-text": "^9.0.4", "@types/jsonwebtoken": "^9.0.10", "rimraf": "^6.0.1", "tsup": "^8.5.0", "tsx": "^4.20.5", "typescript": "^5.9.2"}}