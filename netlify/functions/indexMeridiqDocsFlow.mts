import { googleAI, textEmbedding004 } from '@genkit-ai/googleai';
import type { Context } from "@netlify/functions";
import { genkit } from "genkit";
import pinecone from "genkitx-pinecone";
import { indexMeridiqDocsFlow } from '../../src/indexMeridiqDocs';
import { getCallableJSON, getHttpStatus } from 'genkit/context';
import { configureCustomPineconeRetriever } from '../../src/retriever/customPineconeRetriever';

const ai = genkit({
  plugins: [
    googleAI(), // Provide the key via the GOOGLE_GENAI_API_KEY environment variable or arg { apiKey: 'yourkey'}
    pinecone([
      {
        indexId: 'data',
        embedder: textEmbedding004,
      },
    ]),
  ],
});

configureCustomPineconeRetriever(ai, {
  indexId: 'data',
  embedder: textEmbedding004,
});

const flow = indexMeridiqDocsFlow(ai);

export default async (req: Request, context: Context) => {
  const input = await req.json();

  try {

    const result = await flow.run(input as any);

    return Response.json(result.result);
  } catch (error) {
    return Response.json(getCallableJSON(error), {
      status: getHttpStatus(error),
    })
  }
}
