import { googleA<PERSON>, textEmbedding004 } from "@genkit-ai/googleai";
import type { Context } from "@netlify/functions";
import { genkit } from "genkit";
import pinecone from "genkitx-pinecone";
import { chatWithMeridiqFlow } from "../../src/chatWithMeridiq";
import { getCallableJSON, getHttpStatus } from "genkit/context";
import { configureCustomPineconeRetriever } from "../../src/retriever/customPineconeRetriever";

const ai = genkit({
    plugins: [
        googleAI(), // Provide the key via the GOOGLE_GENAI_API_KEY environment variable or arg { apiKey: 'yourkey'}
        pinecone([
            {
                indexId: "data",
                embedder: textEmbedding004,
            },
        ]),
    ],
});

configureCustomPineconeRetriever(ai, {
    indexId: 'data',
    embedder: textEmbedding004,
  });

const streamDelimiter = "\n\n";

const flow = chatWithMeridiqFlow(ai);

export default async (req: Request, context: Context) => {
    if (req.method === "OPTIONS") {
        const res = new Response();

        res.headers.set("Access-Control-Allow-Origin", "*");
        res.headers.append("Access-Control-Allow-Headers", "*");
        res.headers.append("Access-Control-Allow-Methods", "*");

        return res;
    }

    if (req.method === "POST") {
        const encoder = new TextEncoder();

        const input = await req.json();

        const stream = new URL(req.url).searchParams.get("stream");

        try {
            if (!stream || stream === "false") {
                const result = await flow.run(input as any);

                return Response.json(
                    {
                        result: result.result,
                    },
                    {
                        headers: {
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Methods": "*",
                            "Access-Control-Allow-Headers": "*",
                        },
                    },
                );
            }

            const readable = new ReadableStream({
                start(controller) {
                    flow.run(input as any, {
                        onChunk: (chunk) => {
                            controller.enqueue(
                                encoder.encode("data: " + JSON.stringify({ message: chunk }) + streamDelimiter),
                            );
                        },
                    })
                        .then((data) => {
                            controller.enqueue(
                                encoder.encode("data: " + JSON.stringify({ result: data.result }) + streamDelimiter),
                            );
                        })
                        .finally(async () => {
                            controller.close();
                        });
                },
            });

            return new Response(readable, {
                headers: {
                    "Content-Type": "text/event-stream",
                    "Transfer-Encoding": "chunked",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "*",
                    "Access-Control-Allow-Headers": "*",
                },
            });
        } catch (error) {
            return Response.json(getCallableJSON(error), {
                status: getHttpStatus(error),
            });
        }
    }
};
