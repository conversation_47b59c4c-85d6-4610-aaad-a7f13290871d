import { expressHand<PERSON> } from '@genkit-ai/express';
import { googleAI, textEmbedding004 } from '@genkit-ai/googleai';
import express, { Router } from "express";
import { genkit } from 'genkit';
import { pinecone } from 'genkitx-pinecone';
import serverless from "serverless-http";
import { chatWithMeridiqFlow } from '../../src/chatWithMeridiq';
import { indexMeridiqDocsFlow } from '../../src/indexMeridiqDocs';
import { configureCustomPineconeRetriever } from '../../src/retriever/customPineconeRetriever';

export const ai = genkit({
  plugins: [
    googleAI(), // Provide the key via the GOOGLE_GENAI_API_KEY environment variable or arg { apiKey: 'yourkey'}
    pinecone([
      {
        indexId: 'data',
        embedder: textEmbedding004,
      },
    ]),
  ],
});

configureCustomPineconeRetriever(ai, {
  indexId: 'data',
  embedder: textEmbedding004,
});

const app = express();
app.use(express.json());

const router = Router();

router.post('/indexMeridiqDocsFlow', expressHandler(indexMeridiqDocsFlow(ai)));
router.post('/chatWithMeridiqFlow', expressHandler(chatWithMeridiqFlow(ai)));

app.use("/api/", router);

export const handler = serverless(app);

