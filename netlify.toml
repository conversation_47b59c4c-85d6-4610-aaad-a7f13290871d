# [functions]
#   external_node_modules = ["express"]
#   node_bundler = "esbuild"
# [[redirects]]
#   force = true
#   from = "/api/*"
#   status = 200
#   to = "/.netlify/functions/api/:splat"

[[headers]]
  for = "/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"

[[redirects]]
  from = "/api/*" # simplify all calls to serverless functions
  to = "/.netlify/functions/:splat" # all function calls will go to this path
  status = 200 # ok code
  headers = { Access-Control-Allow-Origin = "*" }